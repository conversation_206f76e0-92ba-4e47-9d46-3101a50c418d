<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DB电竞平台</title>
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #0d1117;
            color: #fff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ff5e5b;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #ff5e5b;
        }
        .balance {
            background-color: #161b22;
            padding: 10px 15px;
            border-radius: 5px;
        }
        .matches-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 30px;
        }
        .match-card {
            background-color: #161b22;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease;
        }
        .match-card:hover {
            transform: translateY(-5px);
        }
        .match-header {
            border-bottom: 1px solid #ff5e5b;
            padding-bottom: 10px;
            margin-bottom: 15px;
        }
        .match-title {
            font-size: 18px;
            font-weight: bold;
        }
        .match-time {
            font-size: 14px;
            color: #aaa;
            margin-top: 5px;
        }
        .teams {
            display: flex;
            justify-content: space-between;
            margin-bottom: 15px;
        }
        .team {
            text-align: center;
            width: 45%;
        }
        .team-name {
            font-weight: bold;
            margin-bottom: 5px;
        }
        .vs {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 10%;
        }
        .odds {
            display: flex;
            justify-content: space-between;
        }
        .odd-button {
            background-color: #ff5e5b;
            color: white;
            border: none;
            padding: 8px 15px;
            border-radius: 5px;
            cursor: pointer;
            width: 45%;
            font-weight: bold;
        }
        .odd-button:hover {
            background-color: #e24f4c;
        }
        .bet-input {
            margin-top: 10px;
            display: flex;
            justify-content: space-between;
        }
        .bet-input input {
            width: 65%;
            padding: 8px;
            border-radius: 5px;
            border: 1px solid #ff5e5b;
            background-color: #0d1117;
            color: white;
        }
        .bet-input button {
            width: 30%;
            background-color: #3fb950;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        .bet-input button:hover {
            background-color: #2ea043;
        }
        .last-update {
            text-align: right;
            margin-top: 20px;
            font-size: 12px;
            color: #aaa;
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="logo">DB电竞平台</div>
            <div class="balance">余额: <span id="balance">10000</span> 元</div>
        </header>
        
        <div class="matches-container" id="matches">
            <!-- 比赛卡片将通过JavaScript动态生成 -->
        </div>
        
        <div class="last-update">
            最后更新时间: <span id="update-time"></span>
        </div>
    </div>

    <script>
        let balance = 10000;
        let matches = [];
        
        // 模拟比赛数据
        function generateMatches() {
            const teams = [
                "EDG", "RNG", "JDG", "TES", "WE", "IG", "FPX", "LNG", 
                "T1", "Gen.G", "DWG KIA", "Fnatic", "G2", "Cloud9", "Team Liquid"
            ];
            
            const games = ["LOL", "DOTA2", "CSGO", "王者荣耀", "VALORANT"];
            
            const newMatches = [];
            
            // 生成5-10场比赛
            const matchCount = Math.floor(Math.random() * 6) + 5;
            
            for (let i = 0; i < matchCount; i++) {
                // 随机选择两支不同的队伍
                let team1Index = Math.floor(Math.random() * teams.length);
                let team2Index;
                do {
                    team2Index = Math.floor(Math.random() * teams.length);
                } while (team1Index === team2Index);
                
                // 随机生成赔率 (1.5 - 4.0)，确保和BO平台有差异
                const odds1 = (Math.random() * 2.5 + 1.5).toFixed(2);
                const odds2 = (Math.random() * 2.5 + 1.5).toFixed(2);
                
                // 随机选择游戏
                const game = games[Math.floor(Math.random() * games.length)];
                
                // 随机生成比赛时间（未来1-3小时内）
                const hours = Math.floor(Math.random() * 3) + 1;
                const minutes = Math.floor(Math.random() * 60);
                const startTime = new Date();
                startTime.setHours(startTime.getHours() + hours);
                startTime.setMinutes(minutes);
                
                newMatches.push({
                    id: `db-match-${i + 1}`,
                    game: game,
                    team1: teams[team1Index],
                    team2: teams[team2Index],
                    odds1: odds1,
                    odds2: odds2,
                    startTime: startTime
                });
            }
            
            matches = newMatches;
            renderMatches();
            
            // 更新最后更新时间
            document.getElementById('update-time').textContent = new Date().toLocaleString();
        }
        
        // 渲染比赛卡片
        function renderMatches() {
            const matchesContainer = document.getElementById('matches');
            matchesContainer.innerHTML = '';
            
            matches.forEach(match => {
                const matchCard = document.createElement('div');
                matchCard.className = 'match-card';
                matchCard.id = match.id;
                
                const startTimeStr = match.startTime.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
                
                matchCard.innerHTML = `
                    <div class="match-header">
                        <div class="match-title">${match.game}</div>
                        <div class="match-time">开始时间: ${startTimeStr}</div>
                    </div>
                    <div class="teams">
                        <div class="team">
                            <div class="team-name">${match.team1}</div>
                        </div>
                        <div class="vs">VS</div>
                        <div class="team">
                            <div class="team-name">${match.team2}</div>
                        </div>
                    </div>
                    <div class="odds">
                        <button class="odd-button" onclick="selectTeam('${match.id}', '${match.team1}', ${match.odds1})">${match.odds1}</button>
                        <button class="odd-button" onclick="selectTeam('${match.id}', '${match.team2}', ${match.odds2})">${match.odds2}</button>
                    </div>
                    <div class="bet-input" id="bet-input-${match.id}" style="display: none;">
                        <input type="number" id="bet-amount-${match.id}" placeholder="投注金额">
                        <button onclick="placeBet('${match.id}')">投注</button>
                    </div>
                    <div id="selected-team-${match.id}" style="display: none;"></div>
                    <div id="selected-odds-${match.id}" style="display: none;"></div>
                `;
                
                matchesContainer.appendChild(matchCard);
            });
        }
        
        // 选择队伍和赔率
        function selectTeam(matchId, team, odds) {
            document.getElementById(`bet-input-${matchId}`).style.display = 'flex';
            document.getElementById(`selected-team-${matchId}`).textContent = team;
            document.getElementById(`selected-team-${matchId}`).style.display = 'none';
            document.getElementById(`selected-odds-${matchId}`).textContent = odds;
            document.getElementById(`selected-odds-${matchId}`).style.display = 'none';
        }
        
        // 投注
        function placeBet(matchId) {
            const betAmount = parseFloat(document.getElementById(`bet-amount-${matchId}`).value);
            const team = document.getElementById(`selected-team-${matchId}`).textContent;
            const odds = parseFloat(document.getElementById(`selected-odds-${matchId}`).textContent);
            
            if (isNaN(betAmount) || betAmount <= 0) {
                alert('请输入有效的投注金额');
                return;
            }
            
            if (betAmount > balance) {
                alert('余额不足');
                return;
            }
            
            // 扣除余额
            balance -= betAmount;
            document.getElementById('balance').textContent = balance;
            
            // 投注成功提示
            alert(`投注成功!\n比赛: ${matchId}\n队伍: ${team}\n赔率: ${odds}\n金额: ${betAmount} 元`);
            
            // 隐藏投注输入框
            document.getElementById(`bet-input-${matchId}`).style.display = 'none';
            document.getElementById(`bet-amount-${matchId}`).value = '';
        }
        
        // 初始化并每20秒更新一次数据
        generateMatches();
        setInterval(generateMatches, 20000);
    </script>
</body>
</html>
