#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的浏览器测试程序
用于验证playwright是否正常工作
"""

import asyncio
import os
from playwright.async_api import async_playwright

async def test_browser():
    """测试浏览器初始化"""
    try:
        print("正在启动playwright...")
        playwright = await async_playwright().start()
        
        print("正在启动浏览器...")
        browser = await playwright.chromium.launch(headless=False)
        
        print("正在创建页面...")
        page = await browser.new_page()
        
        # 测试本地HTML文件
        html_path = os.path.abspath('bo_platform.html')
        # 使用pathlib处理路径，避免中文编码问题
        from pathlib import Path
        bo_url = Path(html_path).as_uri()
        print(f"HTML文件路径: {html_path}")
        print(f"正在导航到: {bo_url}")
        
        await page.goto(bo_url)
        
        print("等待页面加载...")
        await page.wait_for_selector('#balance', state='attached', timeout=10000)
        
        # 获取余额文本
        balance_text = await page.eval_on_selector("#balance", "el => el.textContent")
        print(f"获取到余额: {balance_text}")
        
        # 获取页面标题
        title = await page.title()
        print(f"页面标题: {title}")
        
        print("测试成功！")
        
        # 等待5秒让用户看到浏览器
        await asyncio.sleep(5)
        
        await browser.close()
        await playwright.stop()
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("开始浏览器测试...")
    result = asyncio.run(test_browser())
    
    if result:
        print("✅ 浏览器测试通过")
    else:
        print("❌ 浏览器测试失败")
        print("\n可能的解决方案:")
        print("1. 安装playwright: pip install playwright")
        print("2. 安装浏览器: playwright install chromium")
        print("3. 检查HTML文件是否存在")

if __name__ == "__main__":
    main()
