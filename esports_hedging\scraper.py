import os
import json
import asyncio
import time
from datetime import datetime
from pathlib import Path
from playwright.async_api import async_playwright

class PlatformScraper:
    def __init__(self):
        """初始化平台数据采集器"""
        # 本地HTML文件路径 - 使用pathlib处理中文路径
        # 获取当前脚本所在目录
        script_dir = Path(__file__).parent
        bo_path = script_dir / 'bo_platform.html'
        db_path = script_dir / 'db_platform.html'

        # 确保文件存在
        if not bo_path.exists():
            print(f"警告: BO平台HTML文件不存在: {bo_path}")
        if not db_path.exists():
            print(f"警告: DB平台HTML文件不存在: {db_path}")

        self.bo_url = bo_path.as_uri()
        self.db_url = db_path.as_uri()

        print(f"BO平台URL: {self.bo_url}")
        print(f"DB平台URL: {self.db_url}")
        
        # 浏览器实例
        self.bo_browser = None
        self.db_browser = None
        self.bo_page = None
        self.db_page = None
        
        # 缓存的数据
        self.cached_data = {
            "bo_balance": 10000,
            "db_balance": 10000,
            "matches": []
        }
        self.last_update = datetime.now()
    
    async def initialize_browsers(self):
        """初始化浏览器实例"""
        try:
            print("正在启动playwright...")
            self.playwright = await async_playwright().start()

            # 创建浏览器实例
            print("正在启动浏览器...")
            self.bo_browser = await self.playwright.chromium.launch(headless=False)
            self.db_browser = await self.playwright.chromium.launch(headless=False)

            # 创建页面
            print("正在创建页面...")
            self.bo_page = await self.bo_browser.new_page()
            self.db_page = await self.db_browser.new_page()

            # 导航到平台页面
            print(f"正在导航到BO平台: {self.bo_url}")
            await self.bo_page.goto(self.bo_url)
            print(f"正在导航到DB平台: {self.db_url}")
            await self.db_page.goto(self.db_url)

            # 等待余额元素出现
            print("等待页面元素加载...")
            await self.bo_page.wait_for_selector('#balance', state='attached', timeout=10000)
            await self.db_page.wait_for_selector('#balance', state='attached', timeout=10000)

            # 等待JavaScript生成比赛项
            await asyncio.sleep(3)

            print("浏览器初始化完成")
            return True
        except Exception as e:
            print(f"浏览器初始化失败: {str(e)}")
            # 清理已创建的资源
            await self.close_browsers()
            return False
    
    async def close_browsers(self):
        """关闭浏览器实例"""
        if self.bo_browser:
            await self.bo_browser.close()
            self.bo_browser = None
        
        if self.db_browser:
            await self.db_browser.close()
            self.db_browser = None
        
        if hasattr(self, 'playwright') and self.playwright:
            await self.playwright.stop()
        
        return True
    
    async def get_bo_data(self):
        """获取BO平台数据"""
        if not self.bo_page:
            return None
        
        try:
            # 确保元素存在
            await self.bo_page.wait_for_selector("#balance", state="attached", timeout=5000)
            
            # 获取余额
            balance_text = await self.bo_page.eval_on_selector("#balance", "el => el.textContent")
            balance = float(balance_text)
            
            # 获取比赛数据
            matches_data = await self.bo_page.evaluate('''
            () => {
                const matchData = [];
                const matchCards = document.querySelectorAll('.match-card');
                
                matchCards.forEach(card => {
                    const id = card.id;
                    const game = card.querySelector('.match-title').textContent;
                    const team1 = card.querySelector('.teams .team:first-child .team-name').textContent;
                    const team2 = card.querySelector('.teams .team:last-child .team-name').textContent;
                    const odds1 = parseFloat(card.querySelectorAll('.odds .odd-button')[0].textContent);
                    const odds2 = parseFloat(card.querySelectorAll('.odds .odd-button')[1].textContent);
                    const startTime = card.querySelector('.match-time').textContent.replace('开始时间: ', '');
                    
                    matchData.push({
                        id,
                        platform: 'BO',
                        game,
                        team1,
                        team2,
                        odds1,
                        odds2,
                        start_time: startTime
                    });
                });
                
                return matchData;
            }
            ''')
            
            return {
                "balance": balance,
                "matches": matches_data
            }
        
        except Exception as e:
            print(f"获取BO平台数据出错: {str(e)}")
            return None
    
    async def get_db_data(self):
        """获取DB平台数据"""
        if not self.db_page:
            return None
        
        try:
            # 确保元素存在
            await self.db_page.wait_for_selector("#balance", state="attached", timeout=5000)
            
            # 获取余额
            balance_text = await self.db_page.eval_on_selector("#balance", "el => el.textContent")
            balance = float(balance_text)
            
            # 获取比赛数据
            matches_data = await self.db_page.evaluate('''
            () => {
                const matchData = [];
                const matchCards = document.querySelectorAll('.match-card');
                
                matchCards.forEach(card => {
                    const id = card.id;
                    const game = card.querySelector('.match-title').textContent;
                    const team1 = card.querySelector('.teams .team:first-child .team-name').textContent;
                    const team2 = card.querySelector('.teams .team:last-child .team-name').textContent;
                    const odds1 = parseFloat(card.querySelectorAll('.odds .odd-button')[0].textContent);
                    const odds2 = parseFloat(card.querySelectorAll('.odds .odd-button')[1].textContent);
                    const startTime = card.querySelector('.match-time').textContent.replace('开始时间: ', '');
                    
                    matchData.push({
                        id,
                        platform: 'DB',
                        game,
                        team1,
                        team2,
                        odds1,
                        odds2,
                        start_time: startTime
                    });
                });
                
                return matchData;
            }
            ''')
            
            return {
                "balance": balance,
                "matches": matches_data
            }
        
        except Exception as e:
            print(f"获取DB平台数据出错: {str(e)}")
            return None
    
    def get_all_platform_data(self):
        """获取所有平台数据（同步方法）"""
        # 如果距离上次更新时间不足5秒，则返回缓存数据
        if (datetime.now() - self.last_update).total_seconds() < 5:
            return self.cached_data

        # 检查浏览器是否已初始化
        if not self.bo_page or not self.db_page:
            print("浏览器未初始化，返回缓存数据")
            return self.cached_data

        # 运行异步任务获取数据
        try:
            # 检查是否已有事件循环在运行
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果已有循环在运行，创建新的线程来运行异步代码
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(self._get_data_sync)
                        return future.result(timeout=10)
                else:
                    return self._get_data_sync()
            except RuntimeError:
                # 没有事件循环，创建新的
                return self._get_data_sync()

        except Exception as e:
            print(f"获取所有平台数据出错: {str(e)}")
            return self.cached_data

    def _get_data_sync(self):
        """同步获取数据的辅助方法"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)

        try:
            bo_data = loop.run_until_complete(self.get_bo_data())
            db_data = loop.run_until_complete(self.get_db_data())

            # 合并数据
            all_matches = []
            if bo_data and "matches" in bo_data:
                all_matches.extend(bo_data["matches"])

            if db_data and "matches" in db_data:
                all_matches.extend(db_data["matches"])

            # 更新缓存
            self.cached_data = {
                "bo_balance": bo_data["balance"] if bo_data and "balance" in bo_data else self.cached_data["bo_balance"],
                "db_balance": db_data["balance"] if db_data and "balance" in db_data else self.cached_data["db_balance"],
                "matches": all_matches
            }

            self.last_update = datetime.now()

            return self.cached_data

        except Exception as e:
            print(f"同步获取数据出错: {str(e)}")
            return self.cached_data
        finally:
            loop.close()
    
    async def place_bet(self, platform, match_id, team, amount):
        """在指定平台下注"""
        page = self.bo_page if platform == "BO" else self.db_page
        
        if not page:
            return False, "平台浏览器未初始化"
        
        try:
            # 确保元素存在
            await page.wait_for_selector("#balance", state="attached", timeout=5000)
            
            # 检查余额是否足够
            balance_text = await page.eval_on_selector("#balance", "el => el.textContent")
            balance = float(balance_text)
            
            if balance < amount:
                return False, "余额不足"
            
            # 找到对应的比赛卡片
            match_card = await page.query_selector(f"#{match_id}")
            if not match_card:
                return False, f"找不到比赛 {match_id}"
            
            # 找到对应的队伍按钮并点击
            team_buttons = await match_card.query_selector_all(".odds .odd-button")
            team_names = await match_card.evaluate('''
            (card) => {
                const team1 = card.querySelector('.teams .team:first-child .team-name').textContent;
                const team2 = card.querySelector('.teams .team:last-child .team-name').textContent;
                return [team1, team2];
            }
            ''')
            
            team_index = 0 if team == team_names[0] else 1
            
            # 点击队伍按钮
            await team_buttons[team_index].click()
            
            # 输入投注金额
            bet_input = await match_card.query_selector(f"#bet-amount-{match_id}")
            await bet_input.fill(str(amount))
            
            # 点击投注按钮
            bet_button = await match_card.query_selector(".bet-input button")
            await bet_button.click()
            
            # 等待警告框出现并处理
            dialog_message = await page.evaluate('''
            () => {
                return new Promise(resolve => {
                    window.alert = (message) => {
                        resolve(message);
                        return true;
                    };
                });
            }
            ''')
            
            # 更新缓存中的余额
            if platform == "BO":
                self.cached_data["bo_balance"] = balance - amount
            else:
                self.cached_data["db_balance"] = balance - amount
            
            return True, f"投注成功：{dialog_message}"
        
        except Exception as e:
            return False, f"投注失败: {str(e)}"
