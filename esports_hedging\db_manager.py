import sqlite3
import os
import csv
from datetime import datetime

class DatabaseManager:
    def __init__(self, db_path):
        """初始化数据库管理器"""
        self.db_path = db_path
        self.initialize_db()
    
    def initialize_db(self):
        """初始化数据库表"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        # 创建对冲历史表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS hedge_history (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            timestamp TEXT,
            match_id TEXT,
            match_name TEXT,
            team TEXT,
            platform1 TEXT,
            platform2 TEXT,
            odds1 REAL,
            odds2 REAL,
            amount REAL,
            expected_profit REAL,
            status TEXT
        )
        ''')
        
        # 创建平台余额表
        cursor.execute('''
        CREATE TABLE IF NOT EXISTS platform_balance (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            platform TEXT UNIQUE,
            balance REAL,
            last_updated TEXT
        )
        ''')
        
        # 初始化平台余额数据
        platforms = [("BO", 10000.0), ("DB", 10000.0)]
        for platform, balance in platforms:
            cursor.execute('''
            INSERT OR IGNORE INTO platform_balance (platform, balance, last_updated)
            VALUES (?, ?, ?)
            ''', (platform, balance, datetime.now().strftime("%Y-%m-%d %H:%M:%S")))
        
        conn.commit()
        conn.close()
    
    def record_hedge(self, match_id, match_name, team, platform1, platform2, odds1, odds2, amount, expected_profit, status="完成"):
        """记录对冲操作"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        cursor.execute('''
        INSERT INTO hedge_history (
            timestamp, match_id, match_name, team, platform1, platform2,
            odds1, odds2, amount, expected_profit, status
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (timestamp, match_id, match_name, team, platform1, platform2,
              odds1, odds2, amount, expected_profit, status))
        
        conn.commit()
        conn.close()
        
        return True
    
    def get_hedge_history(self, limit=100):
        """获取对冲历史记录"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT * FROM hedge_history ORDER BY timestamp DESC LIMIT ?
        ''', (limit,))
        
        rows = cursor.fetchall()
        
        # 将数据库行转换为字典列表
        history = []
        for row in rows:
            history.append({
                "time": row["timestamp"],
                "match": row["match_name"],
                "team": row["team"],
                "platform1": row["platform1"],
                "platform2": row["platform2"],
                "odds1": row["odds1"],
                "odds2": row["odds2"],
                "amount": row["amount"],
                "expected_profit": row["expected_profit"],
                "status": row["status"]
            })
        
        conn.close()
        
        return history
    
    def update_balance(self, platform, new_balance):
        """更新平台余额"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        cursor.execute('''
        UPDATE platform_balance SET balance = ?, last_updated = ?
        WHERE platform = ?
        ''', (new_balance, timestamp, platform))
        
        conn.commit()
        conn.close()
        
        return True
    
    def get_balance(self, platform):
        """获取平台余额"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT balance FROM platform_balance WHERE platform = ?
        ''', (platform,))
        
        result = cursor.fetchone()
        conn.close()
        
        if result:
            return result[0]
        else:
            return 0
    
    def export_history_to_csv(self, file_path):
        """将对冲历史导出为CSV文件"""
        history = self.get_hedge_history(limit=1000)  # 获取较多的历史记录
        
        with open(file_path, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            
            # 写入表头
            writer.writerow(["时间", "比赛", "队伍", "平台1", "平台2", "平台1赔率", "平台2赔率", "投注金额", "预期利润", "状态"])
            
            # 写入数据
            for record in history:
                writer.writerow([
                    record["time"],
                    record["match"],
                    record["team"],
                    record["platform1"],
                    record["platform2"],
                    record["odds1"],
                    record["odds2"],
                    record["amount"],
                    record["expected_profit"],
                    record["status"]
                ])
        
        return True
    
    def get_total_profit(self):
        """计算总利润"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('''
        SELECT SUM(expected_profit) FROM hedge_history WHERE status = '完成'
        ''')
        
        result = cursor.fetchone()
        conn.close()
        
        if result and result[0] is not None:
            return result[0]
        else:
            return 0
    
    def clear_history(self):
        """清空历史记录（慎用）"""
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM hedge_history')
        
        conn.commit()
        conn.close()
        
        return True
