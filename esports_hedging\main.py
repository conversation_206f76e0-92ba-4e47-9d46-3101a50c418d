import sys
import os
import json
import time
import asyncio
import threading
from datetime import datetime
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                            QLabel, QPushButton, QTableWidget, QTableWidgetItem, QHeaderView, 
                            QComboBox, QDoubleSpinBox, QTabWidget, QMessageBox, QProgressBar,
                            QFrame, QSplitter, QTextEdit, QCheckBox, QGroupBox, QFormLayout,
                            QLineEdit, QSpinBox, QFileDialog, QGridLayout, QToolBar, QAction,
                            QStatusBar)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, pyqtSlot, QTimer, QSettings
from PyQt5.QtGui import QIcon, QFont, QColor, QPalette

from scraper import PlatformScraper
from hedging import HedgingEngine
from db_manager import DatabaseManager
from config import DEFAULT_CONFIG

class BrowserThread(QThread):
    """独立的浏览器线程，完全隔离浏览器操作"""
    browser_ready_signal = pyqtSignal(bool)
    data_update_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)
    log_signal = pyqtSignal(str)

    def __init__(self, parent=None):
        super().__init__(parent)
        self.running = True
        self.scraper = None

    def run(self):
        """在独立线程中运行浏览器"""
        try:
            # 在这个线程中创建scraper实例
            from scraper import PlatformScraper
            self.scraper = PlatformScraper()

            self.log_signal.emit("正在初始化浏览器...")

            # 创建新的事件循环
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            try:
                # 初始化浏览器
                result = loop.run_until_complete(self.scraper.initialize_browsers())
                if result:
                    self.browser_ready_signal.emit(True)
                    self.log_signal.emit("浏览器初始化成功")

                    # 开始数据采集循环
                    self.data_collection_loop(loop)
                else:
                    self.browser_ready_signal.emit(False)
                    self.error_signal.emit("浏览器初始化失败")
            finally:
                # 清理资源
                if self.scraper:
                    loop.run_until_complete(self.scraper.close_browsers())
                loop.close()

        except Exception as e:
            self.error_signal.emit(f"浏览器线程错误: {str(e)}")
            self.browser_ready_signal.emit(False)

    def data_collection_loop(self, loop):
        """数据采集循环"""
        while self.running:
            try:
                # 异步获取数据
                bo_data = loop.run_until_complete(self.scraper.get_bo_data())
                db_data = loop.run_until_complete(self.scraper.get_db_data())

                # 合并数据
                all_matches = []
                if bo_data and "matches" in bo_data:
                    all_matches.extend(bo_data["matches"])

                if db_data and "matches" in db_data:
                    all_matches.extend(db_data["matches"])

                # 发送数据更新信号
                data = {
                    "bo_balance": bo_data["balance"] if bo_data and "balance" in bo_data else 10000,
                    "db_balance": db_data["balance"] if db_data and "balance" in db_data else 10000,
                    "matches": all_matches
                }

                self.data_update_signal.emit(data)

                # 等待5秒
                for _ in range(50):  # 分成小段等待，便于响应停止信号
                    if not self.running:
                        break
                    time.sleep(0.1)

            except Exception as e:
                self.error_signal.emit(f"数据采集错误: {str(e)}")
                # 出错后等待更长时间
                for _ in range(100):
                    if not self.running:
                        break
                    time.sleep(0.1)

    def stop(self):
        """停止线程"""
        self.running = False
        self.wait(5000)  # 等待最多5秒


class WorkerThread(QThread):
    """简化的工作线程，仅用于兼容性"""
    update_signal = pyqtSignal(dict)
    error_signal = pyqtSignal(str)

    def __init__(self, scraper, parent=None):
        super().__init__(parent)
        self.scraper = scraper
        self.running = True

    def run(self):
        # 这个线程现在只是占位符，实际工作由BrowserThread完成
        while self.running:
            time.sleep(1)

    def stop(self):
        self.running = False
        self.wait()


class HedgingWorker(QThread):
    update_signal = pyqtSignal(dict)
    log_signal = pyqtSignal(str)
    
    def __init__(self, hedging_engine, parent=None):
        super().__init__(parent)
        self.hedging_engine = hedging_engine
        self.running = True
    
    def run(self):
        while self.running:
            try:
                # 计算对冲机会
                opportunities = self.hedging_engine.find_opportunities()
                if opportunities:
                    self.update_signal.emit({"opportunities": opportunities})
                    self.log_signal.emit(f"发现 {len(opportunities)} 个对冲机会")
                time.sleep(2)  # 每2秒计算一次对冲机会
            except Exception as e:
                self.log_signal.emit(f"对冲计算错误: {str(e)}")
                time.sleep(5)
    
    def stop(self):
        self.running = False
        self.wait()


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("电竞对冲软件")
        self.setMinimumSize(1200, 800)
        
        # 初始化组件
        self.db_manager = DatabaseManager("hedge_data.db")
        # scraper将在独立线程中创建
        self.scraper = None
        self.hedging_engine = None
        
        # 加载设置
        self.settings = QSettings("ESportsHedge", "Hedging")
        self.load_settings()
        
        # 初始化UI
        self.init_ui()

        # 初始化线程变量
        self.worker_thread = None
        self.hedging_thread = None

        # 延迟启动浏览器和线程
        QTimer.singleShot(1000, self.delayed_start)
        
        # 定时器保存状态
        self.save_timer = QTimer(self)
        self.save_timer.timeout.connect(self.save_state)
        self.save_timer.start(60000)  # 每分钟保存一次状态
        
        self.statusBar().showMessage("软件已启动")

    def delayed_start(self):
        """延迟启动浏览器和线程"""
        try:
            # 启动独立的浏览器线程
            self.browser_thread = BrowserThread()
            self.browser_thread.browser_ready_signal.connect(self.on_browser_ready)
            self.browser_thread.data_update_signal.connect(self.update_data)
            self.browser_thread.error_signal.connect(self.show_error)
            self.browser_thread.log_signal.connect(self.add_log)
            self.browser_thread.start()

            self.add_log("正在启动浏览器线程...")
        except Exception as e:
            self.show_error(f"启动组件失败: {str(e)}")

    def on_browser_ready(self, success):
        """浏览器准备就绪回调"""
        if success:
            self.add_log("浏览器已准备就绪")
            # 现在可以初始化对冲引擎
            try:
                from hedging import HedgingEngine
                self.hedging_engine = HedgingEngine(None, initial_balance=10000)  # 暂时不传scraper

                # 启动对冲计算线程
                self.hedging_thread = HedgingWorker(self.hedging_engine)
                self.hedging_thread.update_signal.connect(self.update_opportunities)
                self.hedging_thread.log_signal.connect(self.add_log)
                self.hedging_thread.start()

                self.add_log("所有组件已启动")
            except Exception as e:
                self.show_error(f"初始化对冲引擎失败: {str(e)}")
        else:
            self.show_error("浏览器初始化失败")

    def init_ui(self):
        # 创建工具栏
        toolbar = QToolBar("主工具栏")
        self.addToolBar(toolbar)
        
        # 工具栏按钮
        start_action = QAction("启动浏览器", self)
        start_action.triggered.connect(self.start_browsers)
        toolbar.addAction(start_action)
        
        stop_action = QAction("停止", self)
        stop_action.triggered.connect(self.stop_all)
        toolbar.addAction(stop_action)
        
        config_action = QAction("配置", self)
        config_action.triggered.connect(self.show_config)
        toolbar.addAction(config_action)
        
        # 创建主布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Vertical)
        main_layout.addWidget(splitter)
        
        # 上部分：平台数据和对冲机会
        top_widget = QWidget()
        top_layout = QVBoxLayout(top_widget)
        splitter.addWidget(top_widget)
        
        # 标签页
        self.tabs = QTabWidget()
        top_layout.addWidget(self.tabs)
        
        # 平台数据标签页
        platforms_tab = QWidget()
        platforms_layout = QVBoxLayout(platforms_tab)
        
        # 余额显示
        balance_frame = QFrame()
        balance_frame.setFrameShape(QFrame.StyledPanel)
        balance_layout = QHBoxLayout(balance_frame)
        
        self.bo_balance_label = QLabel("BO平台余额: 10000元")
        self.bo_balance_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        balance_layout.addWidget(self.bo_balance_label)
        
        self.db_balance_label = QLabel("DB平台余额: 10000元")
        self.db_balance_label.setStyleSheet("font-size: 14px; font-weight: bold;")
        balance_layout.addWidget(self.db_balance_label)
        
        platforms_layout.addWidget(balance_frame)
        
        # 平台数据表格
        self.platform_data_label = QLabel("平台数据:")
        platforms_layout.addWidget(self.platform_data_label)
        
        self.platform_table = QTableWidget()
        self.platform_table.setColumnCount(8)
        self.platform_table.setHorizontalHeaderLabels(["ID", "平台", "比赛类型", "队伍1", "队伍2", "队伍1赔率", "队伍2赔率", "开始时间"])
        self.platform_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        platforms_layout.addWidget(self.platform_table)
        
        self.tabs.addTab(platforms_tab, "平台数据")
        
        # 对冲机会标签页
        opportunities_tab = QWidget()
        opportunities_layout = QVBoxLayout(opportunities_tab)
        
        self.opportunities_label = QLabel("对冲机会:")
        opportunities_layout.addWidget(self.opportunities_label)
        
        self.opportunities_table = QTableWidget()
        self.opportunities_table.setColumnCount(9)
        self.opportunities_table.setHorizontalHeaderLabels(["ID", "比赛类型", "队伍", "平台1", "平台1赔率", "平台2", "平台2赔率", "利润率", "操作"])
        self.opportunities_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        opportunities_layout.addWidget(self.opportunities_table)
        
        # 对冲设置区域
        hedge_settings_group = QGroupBox("对冲设置")
        hedge_settings_layout = QFormLayout(hedge_settings_group)
        
        self.hedge_amount_input = QDoubleSpinBox()
        self.hedge_amount_input.setRange(100, 10000)
        self.hedge_amount_input.setValue(1000)
        self.hedge_amount_input.setSingleStep(100)
        hedge_settings_layout.addRow("对冲金额:", self.hedge_amount_input)
        
        self.min_profit_input = QDoubleSpinBox()
        self.min_profit_input.setRange(0.01, 0.5)
        self.min_profit_input.setValue(0.05)
        self.min_profit_input.setSingleStep(0.01)
        self.min_profit_input.setDecimals(2)
        hedge_settings_layout.addRow("最小利润率:", self.min_profit_input)
        
        self.auto_hedge_checkbox = QCheckBox("自动对冲")
        hedge_settings_layout.addRow("", self.auto_hedge_checkbox)
        
        opportunities_layout.addWidget(hedge_settings_group)
        
        self.tabs.addTab(opportunities_tab, "对冲机会")
        
        # 对冲历史标签页
        history_tab = QWidget()
        history_layout = QVBoxLayout(history_tab)
        
        self.history_table = QTableWidget()
        self.history_table.setColumnCount(8)
        self.history_table.setHorizontalHeaderLabels(["时间", "比赛", "队伍", "平台1", "平台2", "投注金额", "预期利润", "状态"])
        self.history_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        history_layout.addWidget(self.history_table)
        
        self.tabs.addTab(history_tab, "对冲历史")
        
        # 下部分：日志和控制
        bottom_widget = QWidget()
        bottom_layout = QVBoxLayout(bottom_widget)
        splitter.addWidget(bottom_widget)
        
        # 日志区域
        log_group = QGroupBox("运行日志")
        log_layout = QVBoxLayout(log_group)
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        
        bottom_layout.addWidget(log_group)
        
        # 控制区域
        control_group = QGroupBox("控制面板")
        control_layout = QHBoxLayout(control_group)
        
        self.refresh_button = QPushButton("刷新数据")
        self.refresh_button.clicked.connect(self.refresh_data)
        control_layout.addWidget(self.refresh_button)
        
        self.clear_log_button = QPushButton("清除日志")
        self.clear_log_button.clicked.connect(self.clear_log)
        control_layout.addWidget(self.clear_log_button)
        
        self.export_button = QPushButton("导出数据")
        self.export_button.clicked.connect(self.export_data)
        control_layout.addWidget(self.export_button)
        
        bottom_layout.addWidget(control_group)
        
        # 设置分割器比例
        splitter.setSizes([600, 200])
        
        # 状态栏
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("准备就绪")
        
        # 初始添加日志
        self.add_log("系统初始化完成")
    
    def update_data(self, data):
        """更新平台数据"""
        if not data:
            return
        
        # 更新余额
        if 'bo_balance' in data:
            self.bo_balance_label.setText(f"BO平台余额: {data['bo_balance']}元")
        if 'db_balance' in data:
            self.db_balance_label.setText(f"DB平台余额: {data['db_balance']}元")
        
        # 更新表格
        if 'matches' in data:
            matches = data['matches']
            self.platform_table.setRowCount(len(matches))
            
            for i, match in enumerate(matches):
                self.platform_table.setItem(i, 0, QTableWidgetItem(match['id']))
                self.platform_table.setItem(i, 1, QTableWidgetItem(match['platform']))
                self.platform_table.setItem(i, 2, QTableWidgetItem(match['game']))
                self.platform_table.setItem(i, 3, QTableWidgetItem(match['team1']))
                self.platform_table.setItem(i, 4, QTableWidgetItem(match['team2']))
                self.platform_table.setItem(i, 5, QTableWidgetItem(str(match['odds1'])))
                self.platform_table.setItem(i, 6, QTableWidgetItem(str(match['odds2'])))
                self.platform_table.setItem(i, 7, QTableWidgetItem(match['start_time']))
        
        self.status_bar.showMessage(f"数据已更新: {datetime.now().strftime('%H:%M:%S')}")
    
    def update_opportunities(self, data):
        """更新对冲机会"""
        if 'opportunities' not in data:
            return
        
        opportunities = data['opportunities']
        self.opportunities_table.setRowCount(len(opportunities))
        
        for i, opp in enumerate(opportunities):
            self.opportunities_table.setItem(i, 0, QTableWidgetItem(opp['id']))
            self.opportunities_table.setItem(i, 1, QTableWidgetItem(opp['game']))
            self.opportunities_table.setItem(i, 2, QTableWidgetItem(opp['team']))
            self.opportunities_table.setItem(i, 3, QTableWidgetItem(opp['platform1']))
            self.opportunities_table.setItem(i, 4, QTableWidgetItem(str(opp['odds1'])))
            self.opportunities_table.setItem(i, 5, QTableWidgetItem(opp['platform2']))
            self.opportunities_table.setItem(i, 6, QTableWidgetItem(str(opp['odds2'])))
            self.opportunities_table.setItem(i, 7, QTableWidgetItem(f"{opp['profit_rate']:.2%}"))
            
            # 添加对冲按钮
            hedge_button = QPushButton("对冲")
            hedge_button.setProperty("opportunity_id", opp['id'])
            hedge_button.clicked.connect(lambda _, btn=hedge_button: self.execute_hedge(btn.property("opportunity_id")))
            self.opportunities_table.setCellWidget(i, 8, hedge_button)
            
            # 如果设置了自动对冲且利润率高于设定值，则自动执行
            min_profit = self.min_profit_input.value()
            if self.auto_hedge_checkbox.isChecked() and opp['profit_rate'] >= min_profit:
                self.execute_hedge(opp['id'])
    
    def execute_hedge(self, opportunity_id):
        """执行对冲操作"""
        try:
            amount = self.hedge_amount_input.value()
            result = self.hedging_engine.execute_hedge(opportunity_id, amount)
            if result['success']:
                self.add_log(f"对冲成功: {result['message']}")
                self.update_history_table()
                QMessageBox.information(self, "对冲成功", result['message'])
            else:
                self.add_log(f"对冲失败: {result['message']}")
                QMessageBox.warning(self, "对冲失败", result['message'])
        except Exception as e:
            self.add_log(f"对冲操作错误: {str(e)}")
            QMessageBox.critical(self, "错误", f"对冲操作失败: {str(e)}")
    
    def update_history_table(self):
        """更新对冲历史表格"""
        history = self.db_manager.get_hedge_history()
        self.history_table.setRowCount(len(history))
        
        for i, record in enumerate(history):
            self.history_table.setItem(i, 0, QTableWidgetItem(record['time']))
            self.history_table.setItem(i, 1, QTableWidgetItem(record['match']))
            self.history_table.setItem(i, 2, QTableWidgetItem(record['team']))
            self.history_table.setItem(i, 3, QTableWidgetItem(record['platform1']))
            self.history_table.setItem(i, 4, QTableWidgetItem(record['platform2']))
            self.history_table.setItem(i, 5, QTableWidgetItem(str(record['amount'])))
            self.history_table.setItem(i, 6, QTableWidgetItem(f"{record['expected_profit']:.2f}"))
            self.history_table.setItem(i, 7, QTableWidgetItem(record['status']))
    
    def add_log(self, message):
        """添加日志"""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_text.append(log_entry)
        
        # 保存日志到文件
        with open("hedge_log.txt", "a", encoding="utf-8") as f:
            f.write(log_entry + "\n")
    
    def clear_log(self):
        """清除日志"""
        self.log_text.clear()
    
    def refresh_data(self):
        """手动刷新数据"""
        # 数据刷新现在由独立的浏览器线程自动处理
        self.add_log("数据刷新由浏览器线程自动处理，无需手动刷新")
    
    def show_error(self, message):
        """显示错误信息"""
        self.add_log(f"错误: {message}")
        self.status_bar.showMessage(f"错误: {message}", 5000)
    
    def export_data(self):
        """导出数据"""
        try:
            # 导出对冲历史
            file_path, _ = QFileDialog.getSaveFileName(self, "导出数据", "hedge_history.csv", "CSV文件 (*.csv)")
            if file_path:
                self.db_manager.export_history_to_csv(file_path)
                self.add_log(f"数据已导出到 {file_path}")
                QMessageBox.information(self, "导出成功", f"数据已导出到 {file_path}")
        except Exception as e:
            self.show_error(f"导出数据失败: {str(e)}")
    
    def start_browsers(self):
        """启动浏览器 - 现在通过独立线程处理"""
        self.add_log("浏览器启动现在通过独立线程处理")
        if hasattr(self, 'browser_thread') and self.browser_thread:
            if not self.browser_thread.isRunning():
                self.browser_thread.start()
        else:
            self.delayed_start()
    
    def stop_all(self):
        """停止所有运行的线程和浏览器"""
        try:
            # 停止浏览器线程（这会自动关闭浏览器）
            if hasattr(self, 'browser_thread') and self.browser_thread:
                self.add_log("正在停止浏览器线程...")
                self.browser_thread.stop()

            # 停止对冲线程
            if hasattr(self, 'hedging_thread') and self.hedging_thread:
                self.hedging_thread.stop()

            # 停止工作线程（如果存在）
            if hasattr(self, 'worker_thread') and self.worker_thread:
                self.worker_thread.stop()

            self.add_log("已停止所有操作")
        except Exception as e:
            self.show_error(f"停止操作失败: {str(e)}")
    
    def show_config(self):
        """显示配置对话框"""
        # 这里可以实现配置对话框
        pass
    
    def save_settings(self):
        """保存设置"""
        self.settings.setValue("hedge_amount", self.hedge_amount_input.value())
        self.settings.setValue("min_profit", self.min_profit_input.value())
        self.settings.setValue("auto_hedge", self.auto_hedge_checkbox.isChecked())
    
    def load_settings(self):
        """加载设置"""
        # 这些设置会在初始化UI组件后应用
        self.hedge_amount = self.settings.value("hedge_amount", 1000, type=float)
        self.min_profit = self.settings.value("min_profit", 0.05, type=float)
        self.auto_hedge = self.settings.value("auto_hedge", False, type=bool)
    
    def save_state(self):
        """保存当前状态"""
        self.save_settings()
        self.add_log("状态已保存")
    
    def closeEvent(self, event):
        """关闭窗口事件"""
        reply = QMessageBox.question(self, '确认退出', 
                                     '确定要退出吗？', QMessageBox.Yes | 
                                     QMessageBox.No, QMessageBox.No)

        if reply == QMessageBox.Yes:
            self.save_settings()
            self.stop_all()
            event.accept()
        else:
            event.ignore()


def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')  # 使用Fusion风格
    
    # 设置暗色主题
    palette = QPalette()
    palette.setColor(QPalette.Window, QColor(53, 53, 53))
    palette.setColor(QPalette.WindowText, Qt.white)
    palette.setColor(QPalette.Base, QColor(25, 25, 25))
    palette.setColor(QPalette.AlternateBase, QColor(53, 53, 53))
    palette.setColor(QPalette.ToolTipBase, Qt.white)
    palette.setColor(QPalette.ToolTipText, Qt.white)
    palette.setColor(QPalette.Text, Qt.white)
    palette.setColor(QPalette.Button, QColor(53, 53, 53))
    palette.setColor(QPalette.ButtonText, Qt.white)
    palette.setColor(QPalette.BrightText, Qt.red)
    palette.setColor(QPalette.Link, QColor(42, 130, 218))
    palette.setColor(QPalette.Highlight, QColor(42, 130, 218))
    palette.setColor(QPalette.HighlightedText, Qt.black)
    app.setPalette(palette)
    
    window = MainWindow()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
