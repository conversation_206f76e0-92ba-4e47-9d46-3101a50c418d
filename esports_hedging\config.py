# 默认配置
DEFAULT_CONFIG = {
    # 平台相关配置
    "platforms": {
        "BO": {
            "name": "BO电竞平台",
            "url": "bo_platform.html",
            "initial_balance": 10000.0
        },
        "DB": {
            "name": "DB电竞平台",
            "url": "db_platform.html",
            "initial_balance": 10000.0
        }
    },
    
    # 对冲相关配置
    "hedging": {
        "default_amount": 1000.0,         # 默认对冲金额
        "min_profit_rate": 0.05,          # 最小利润率
        "auto_hedge": False,              # 是否自动对冲
        "max_daily_hedges": 20,           # 每日最大对冲次数
        "refresh_interval": 5,            # 数据刷新间隔（秒）
        "opportunity_refresh": 2,         # 对冲机会刷新间隔（秒）
        "prevent_same_platform": True,    # 防止同平台对冲
        "prevent_same_team": True,        # 防止同队伍对冲
        "minimum_odds": 1.2               # 最小有效赔率
    },
    
    # 界面配置
    "ui": {
        "theme": "dark",                  # 界面主题 (dark/light)
        "window_size": [1200, 800],       # 窗口大小
        "font_size": 12,                  # 字体大小
        "log_max_lines": 1000,            # 日志最大行数
        "table_rows": 20                  # 表格显示行数
    },
    
    # 数据库配置
    "database": {
        "path": "hedge_data.db",          # 数据库文件路径
        "backup_interval": 3600,          # 备份间隔（秒）
        "max_backups": 5                  # 最大备份数量
    },
    
    # 日志配置
    "logging": {
        "file": "hedge_log.txt",          # 日志文件路径
        "level": "INFO",                  # 日志级别
        "max_size": 10485760,             # 最大日志文件大小（字节）
        "backup_count": 3                 # 日志备份数量
    }
}

# 加载配置
def load_config(config_file=None):
    """从文件加载配置，如果文件不存在则使用默认配置"""
    import os
    import json
    
    config = DEFAULT_CONFIG.copy()
    
    if config_file and os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                user_config = json.load(f)
                
                # 递归更新配置
                def update_config(target, source):
                    for key, value in source.items():
                        if key in target and isinstance(target[key], dict) and isinstance(value, dict):
                            update_config(target[key], value)
                        else:
                            target[key] = value
                
                update_config(config, user_config)
        except Exception as e:
            print(f"加载配置文件出错: {str(e)}")
    
    return config

# 保存配置
def save_config(config, config_file):
    """保存配置到文件"""
    import json
    
    try:
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=4)
        return True
    except Exception as e:
        print(f"保存配置文件出错: {str(e)}")
        return False
