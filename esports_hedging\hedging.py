import asyncio
import time
import uuid
from datetime import datetime
from typing import List, Dict, Tuple, Optional

class HedgingEngine:
    def __init__(self, scraper, initial_balance=10000.0, db_manager=None):
        """初始化对冲引擎"""
        self.scraper = scraper
        self.db_manager = db_manager
        self.initial_balance = initial_balance
        
        # 保存当前的对冲机会
        self.current_opportunities = []
        
        # 记录正在执行的对冲操作
        self.active_hedges = {}
    
    def find_opportunities(self, min_profit_rate=0.01):
        """查找对冲机会"""
        # 获取最新的平台数据
        platform_data = self.scraper.get_all_platform_data()
        if not platform_data or "matches" not in platform_data:
            return []
        
        matches = platform_data["matches"]
        opportunities = []
        
        # 按比赛类型和队伍组织数据
        match_dict = {}
        for match in matches:
            game = match["game"]
            teams = (match["team1"], match["team2"])
            
            if game not in match_dict:
                match_dict[game] = {}
            
            if teams not in match_dict[game]:
                match_dict[game][teams] = []
            
            match_dict[game][teams].append(match)
        
        # 对每种比赛类型，寻找可能的对冲机会
        for game, game_matches in match_dict.items():
            for teams, matches_list in game_matches.items():
                # 需要至少两个不同平台的相同比赛才能对冲
                if len(matches_list) < 2:
                    continue
                
                # 检查是否有来自不同平台的比赛
                platforms = set(m["platform"] for m in matches_list)
                if len(platforms) < 2:
                    continue
                
                # 找出每个平台上的最佳赔率
                best_odds = {}
                for platform in platforms:
                    platform_matches = [m for m in matches_list if m["platform"] == platform]
                    if not platform_matches:
                        continue
                    
                    # 假设只有一个来自该平台的比赛记录
                    match = platform_matches[0]
                    best_odds[platform] = {
                        "team1": {
                            "team": match["team1"],
                            "odds": match["odds1"],
                            "match_id": match["id"]
                        },
                        "team2": {
                            "team": match["team2"],
                            "odds": match["odds2"],
                            "match_id": match["id"]
                        }
                    }
                
                # 查找对冲机会 - 比较不同平台上的赔率
                platform_list = list(platforms)
                for i in range(len(platform_list)):
                    for j in range(i + 1, len(platform_list)):
                        platform1 = platform_list[i]
                        platform2 = platform_list[j]
                        
                        # 检查是否可以通过下注team1和team2来实现对冲
                        team1_platform1_odds = best_odds[platform1]["team1"]["odds"]
                        team2_platform2_odds = best_odds[platform2]["team2"]["odds"]
                        
                        # 计算反向对冲的利润率
                        profit_rate1 = self.calculate_profit_rate(team1_platform1_odds, team2_platform2_odds)
                        
                        if profit_rate1 > min_profit_rate:
                            # 发现对冲机会
                            opportunity_id = f"opp-{uuid.uuid4().hex[:8]}"
                            opportunities.append({
                                "id": opportunity_id,
                                "game": game,
                                "team": best_odds[platform1]["team1"]["team"],
                                "platform1": platform1,
                                "platform2": platform2,
                                "odds1": team1_platform1_odds,
                                "odds2": team2_platform2_odds,
                                "profit_rate": profit_rate1,
                                "match_id1": best_odds[platform1]["team1"]["match_id"],
                                "match_id2": best_odds[platform2]["team2"]["match_id"],
                                "type": "反向对冲"
                            })
                        
                        # 检查反向对冲 - team2在platform1，team1在platform2
                        team2_platform1_odds = best_odds[platform1]["team2"]["odds"]
                        team1_platform2_odds = best_odds[platform2]["team1"]["odds"]
                        
                        profit_rate2 = self.calculate_profit_rate(team2_platform1_odds, team1_platform2_odds)
                        
                        if profit_rate2 > min_profit_rate:
                            # 发现对冲机会
                            opportunity_id = f"opp-{uuid.uuid4().hex[:8]}"
                            opportunities.append({
                                "id": opportunity_id,
                                "game": game,
                                "team": best_odds[platform1]["team2"]["team"],
                                "platform1": platform1,
                                "platform2": platform2,
                                "odds1": team2_platform1_odds,
                                "odds2": team1_platform2_odds,
                                "profit_rate": profit_rate2,
                                "match_id1": best_odds[platform1]["team2"]["match_id"],
                                "match_id2": best_odds[platform2]["team1"]["match_id"],
                                "type": "反向对冲"
                            })
        
        # 按利润率排序
        opportunities.sort(key=lambda x: x["profit_rate"], reverse=True)
        
        # 保存当前的对冲机会
        self.current_opportunities = opportunities
        
        return opportunities
    
    def calculate_profit_rate(self, odds1, odds2):
        """计算对冲的利润率"""
        if odds1 <= 1 or odds2 <= 1:
            return -1  # 无效赔率
        
        # 计算凯利公式的对冲利润率
        total_probability = (1 / odds1) + (1 / odds2)
        
        # 如果总概率小于1，则有对冲机会
        if total_probability < 1:
            return 1 - total_probability
        else:
            return -1  # 无对冲机会
    
    def calculate_bet_amounts(self, total_amount, odds1, odds2):
        """计算最优的下注金额分配"""
        if odds1 <= 1 or odds2 <= 1:
            return None, None
        
        # 计算总概率
        total_probability = (1 / odds1) + (1 / odds2)
        
        if total_probability >= 1:
            return None, None  # 无对冲机会
        
        # 计算最优分配比例
        ratio1 = (1 / odds1) / total_probability
        ratio2 = (1 / odds2) / total_probability
        
        # 计算最优下注金额
        amount1 = total_amount * ratio1
        amount2 = total_amount * ratio2
        
        return amount1, amount2
    
    def execute_hedge(self, opportunity_id, total_amount):
        """执行对冲操作"""
        # 查找对冲机会
        opportunity = None
        for opp in self.current_opportunities:
            if opp["id"] == opportunity_id:
                opportunity = opp
                break
        
        if not opportunity:
            return {"success": False, "message": f"找不到对冲机会 {opportunity_id}"}
        
        # 计算最优下注金额
        amount1, amount2 = self.calculate_bet_amounts(
            total_amount, opportunity["odds1"], opportunity["odds2"]
        )
        
        if amount1 is None or amount2 is None:
            return {"success": False, "message": "无法计算有效的下注金额"}
        
        # 防止同平台对冲
        if opportunity["platform1"] == opportunity["platform2"]:
            return {"success": False, "message": "禁止在同一平台对冲"}
        
        # 检查是否有足够的余额
        platform_data = self.scraper.get_all_platform_data()
        if not platform_data:
            return {"success": False, "message": "无法获取平台数据"}
        
        bo_balance = platform_data.get("bo_balance", 0)
        db_balance = platform_data.get("db_balance", 0)
        
        platform1_balance = bo_balance if opportunity["platform1"] == "BO" else db_balance
        platform2_balance = bo_balance if opportunity["platform2"] == "BO" else db_balance
        
        if platform1_balance < amount1:
            return {"success": False, "message": f"{opportunity['platform1']}平台余额不足: 需要 {amount1:.2f}, 当前 {platform1_balance:.2f}"}
        
        if platform2_balance < amount2:
            return {"success": False, "message": f"{opportunity['platform2']}平台余额不足: 需要 {amount2:.2f}, 当前 {platform2_balance:.2f}"}
        
        # 记录正在执行的对冲操作
        hedge_id = f"hedge-{uuid.uuid4().hex[:8]}"
        self.active_hedges[hedge_id] = {
            "opportunity": opportunity,
            "amount1": amount1,
            "amount2": amount2,
            "total_amount": total_amount,
            "expected_profit": total_amount * opportunity["profit_rate"],
            "status": "进行中",
            "timestamp": datetime.now()
        }
        
        # 执行下注操作
        try:
            # 在平台1下注
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            bet1_team = opportunity["team"]
            bet1_success, bet1_message = loop.run_until_complete(
                self.scraper.place_bet(
                    opportunity["platform1"],
                    opportunity["match_id1"],
                    bet1_team,
                    amount1
                )
            )
            
            if not bet1_success:
                self.active_hedges[hedge_id]["status"] = "失败"
                return {"success": False, "message": f"平台1下注失败: {bet1_message}"}
            
            # 在平台2下注（对立队伍）
            bet2_team = opportunity["team"]  # 在反向对冲场景中，team1和team2是一致的
            bet2_success, bet2_message = loop.run_until_complete(
                self.scraper.place_bet(
                    opportunity["platform2"],
                    opportunity["match_id2"],
                    bet2_team,
                    amount2
                )
            )
            
            if not bet2_success:
                self.active_hedges[hedge_id]["status"] = "部分失败"
                return {"success": False, "message": f"平台1下注成功，但平台2下注失败: {bet2_message}"}
            
            # 更新对冲状态
            self.active_hedges[hedge_id]["status"] = "完成"
            
            # 如果提供了数据库管理器，记录对冲历史
            if self.db_manager:
                self.db_manager.record_hedge(
                    opportunity["match_id1"],
                    opportunity["game"],
                    bet1_team,
                    opportunity["platform1"],
                    opportunity["platform2"],
                    opportunity["odds1"],
                    opportunity["odds2"],
                    total_amount,
                    total_amount * opportunity["profit_rate"]
                )
            
            return {
                "success": True,
                "message": f"对冲成功: 平台1下注 {amount1:.2f} @ {opportunity['odds1']}, 平台2下注 {amount2:.2f} @ {opportunity['odds2']}, 预期利润 {total_amount * opportunity['profit_rate']:.2f} ({opportunity['profit_rate']:.2%})"
            }
        
        except Exception as e:
            self.active_hedges[hedge_id]["status"] = "错误"
            return {"success": False, "message": f"执行对冲时出错: {str(e)}"}
        finally:
            loop.close()
    
    def get_active_hedges(self):
        """获取活跃的对冲操作"""
        return self.active_hedges
    
    def get_hedge_summary(self):
        """获取对冲统计摘要"""
        completed = [h for h in self.active_hedges.values() if h["status"] == "完成"]
        failed = [h for h in self.active_hedges.values() if h["status"] != "完成"]
        
        total_profit = sum(h["expected_profit"] for h in completed)
        
        return {
            "total_hedges": len(self.active_hedges),
            "completed_hedges": len(completed),
            "failed_hedges": len(failed),
            "total_profit": total_profit,
            "success_rate": len(completed) / max(1, len(self.active_hedges))
        }
