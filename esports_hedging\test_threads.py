#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新的线程架构
验证浏览器线程是否能正常工作
"""

import sys
import time
from PyQt5.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QLabel
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont

# 导入我们的浏览器线程
from main import BrowserThread

class TestWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("浏览器线程测试")
        self.setMinimumSize(800, 600)
        
        # 创建UI
        self.init_ui()
        
        # 浏览器线程
        self.browser_thread = None
        
    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 状态标签
        self.status_label = QLabel("状态: 未启动")
        self.status_label.setFont(QFont("Arial", 12, QFont.Bold))
        layout.addWidget(self.status_label)
        
        # 按钮
        self.start_button = QPushButton("启动浏览器线程")
        self.start_button.clicked.connect(self.start_browser_thread)
        layout.addWidget(self.start_button)
        
        self.stop_button = QPushButton("停止浏览器线程")
        self.stop_button.clicked.connect(self.stop_browser_thread)
        self.stop_button.setEnabled(False)
        layout.addWidget(self.stop_button)
        
        # 日志区域
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        layout.addWidget(self.log_text)
        
    def start_browser_thread(self):
        """启动浏览器线程"""
        try:
            self.log_text.append("正在启动浏览器线程...")
            
            self.browser_thread = BrowserThread()
            self.browser_thread.browser_ready_signal.connect(self.on_browser_ready)
            self.browser_thread.data_update_signal.connect(self.on_data_update)
            self.browser_thread.error_signal.connect(self.on_error)
            self.browser_thread.log_signal.connect(self.on_log)
            self.browser_thread.start()
            
            self.start_button.setEnabled(False)
            self.stop_button.setEnabled(True)
            self.status_label.setText("状态: 启动中...")
            
        except Exception as e:
            self.log_text.append(f"启动失败: {str(e)}")
    
    def stop_browser_thread(self):
        """停止浏览器线程"""
        if self.browser_thread:
            self.log_text.append("正在停止浏览器线程...")
            self.browser_thread.stop()
            self.browser_thread = None
            
            self.start_button.setEnabled(True)
            self.stop_button.setEnabled(False)
            self.status_label.setText("状态: 已停止")
    
    def on_browser_ready(self, success):
        """浏览器准备就绪回调"""
        if success:
            self.status_label.setText("状态: 浏览器已就绪")
            self.log_text.append("✅ 浏览器线程启动成功！")
        else:
            self.status_label.setText("状态: 浏览器启动失败")
            self.log_text.append("❌ 浏览器线程启动失败")
    
    def on_data_update(self, data):
        """数据更新回调"""
        bo_balance = data.get('bo_balance', 0)
        db_balance = data.get('db_balance', 0)
        matches_count = len(data.get('matches', []))
        
        self.log_text.append(f"📊 数据更新: BO余额={bo_balance}, DB余额={db_balance}, 比赛数={matches_count}")
    
    def on_error(self, error_msg):
        """错误回调"""
        self.log_text.append(f"❌ 错误: {error_msg}")
    
    def on_log(self, log_msg):
        """日志回调"""
        self.log_text.append(f"📝 {log_msg}")
    
    def closeEvent(self, event):
        """关闭事件"""
        if self.browser_thread:
            self.log_text.append("正在关闭浏览器线程...")
            self.browser_thread.stop()
            self.browser_thread.wait(3000)
        event.accept()

def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = TestWindow()
    window.show()
    
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()
