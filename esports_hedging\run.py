#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
电竞对冲软件启动脚本
"""

import os
import sys
import subprocess
import time
import webbrowser
from pathlib import Path

def check_requirements():
    """检查依赖是否安装"""
    try:
        import PyQt5
        import playwright
        return True
    except ImportError as e:
        print(f"缺少必要的依赖: {e}")
        return False

def install_requirements():
    """安装依赖"""
    print("正在安装依赖...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "PyQt5", "playwright"])
        subprocess.check_call([sys.executable, "-m", "playwright", "install"])
        print("依赖安装完成!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"安装依赖失败: {e}")
        return False

def check_files():
    """检查必要文件是否存在"""
    required_files = [
        "main.py",
        "scraper.py",
        "hedging.py",
        "db_manager.py",
        "config.py",
        "bo_platform.html",
        "db_platform.html"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"缺少以下文件: {', '.join(missing_files)}")
        return False
    
    # 检查HTML文件内容是否有效
    html_files = ["bo_platform.html", "db_platform.html"]
    for html_file in html_files:
        if os.path.exists(html_file):
            try:
                with open(html_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'id="balance"' not in content:
                        print(f"警告: {html_file} 中缺少关键元素 'id="balance"'")
                        return False
            except Exception as e:
                print(f"读取 {html_file} 时出错: {e}")
                return False
    
    return True

def start_application():
    """启动应用程序"""
    try:
        subprocess.Popen([sys.executable, "main.py"])
        print("应用程序已启动!")
        return True
    except Exception as e:
        print(f"启动应用程序失败: {e}")
        return False

def open_platforms():
    """打开平台HTML文件"""
    try:
        bo_path = os.path.abspath("bo_platform.html")
        db_path = os.path.abspath("db_platform.html")
        
        webbrowser.open(f"file://{bo_path}")
        time.sleep(1)  # 等待第一个浏览器窗口打开
        webbrowser.open(f"file://{db_path}")
        print("平台页面已打开!")
        return True
    except Exception as e:
        print(f"打开平台页面失败: {e}")
        return False

def main():
    print("=" * 50)
    print("电竞对冲软件启动工具")
    print("=" * 50)
    print()
    
    # 切换到脚本所在目录
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 检查依赖
    if not check_requirements():
        print("是否要安装缺少的依赖? (y/n)")
        choice = input().strip().lower()
        if choice == 'y':
            if not install_requirements():
                print("依赖安装失败，无法继续。")
                return
        else:
            print("未安装依赖，无法继续。")
            return
    
    # 检查文件
    if not check_files():
        print("缺少必要文件，无法继续。")
        return
    
    print("\n请选择操作:")
    print("1. 启动对冲软件")
    print("2. 仅打开平台页面")
    print("3. 全部启动（对冲软件和平台页面）")
    print("4. 退出")
    
    choice = input("请输入选项 (1-4): ").strip()
    
    if choice == '1':
        start_application()
    elif choice == '2':
        open_platforms()
    elif choice == '3':
        start_application()
        time.sleep(2)  # 等待应用程序启动
        open_platforms()
    elif choice == '4':
        print("退出程序")
        return
    else:
        print("无效选项!")
        return
    
    print("\n启动完成!")

if __name__ == "__main__":
    main()
