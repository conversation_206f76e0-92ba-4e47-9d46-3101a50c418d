# 电竞对冲软件

这是一个电竞投注对冲软件，可以自动从不同平台采集赔率数据，发现对冲机会，并执行对冲操作以获取无风险收益。

## 功能特点

- 实时采集BO和DB两个电竞平台的比赛队伍和赔率信息
- 自动计算对冲机会和利润率
- 支持手动和自动对冲操作
- 直观的用户界面，展示平台数据、对冲机会和历史记录
- 内置防止同平台对冲和同队伍对冲的安全机制
- 支持数据导出和日志记录

## 安装要求

- Python 3.7+
- PyQt5
- Playwright

## 快速开始

1. 克隆或下载此仓库
2. 运行启动脚本：
   ```
   python run.py
   ```
3. 脚本将自动检查依赖并提供安装选项
4. 选择启动模式：
   - 启动对冲软件
   - 仅打开平台页面
   - 全部启动（同时启动软件和平台页面）

## 软件说明

### 文件结构

- `main.py`: 主程序和用户界面
- `scraper.py`: 平台数据采集模块
- `hedging.py`: 对冲引擎和算法
- `db_manager.py`: 数据库管理模块
- `config.py`: 配置文件
- `run.py`: 启动脚本
- `bo_platform.html`: BO平台模拟界面
- `db_platform.html`: DB平台模拟界面

### 使用指南

1. **启动软件**:
   运行`run.py`并选择启动模式。

2. **查看平台数据**:
   在"平台数据"标签页可以查看BO和DB两个平台的比赛信息和赔率。

3. **对冲操作**:
   - 在"对冲机会"标签页可以查看当前可用的对冲机会
   - 设置对冲金额和最小利润率
   - 点击"对冲"按钮执行对冲操作，或启用自动对冲

4. **查看历史记录**:
   在"对冲历史"标签页可以查看所有已执行的对冲操作和结果。

5. **导出数据**:
   使用"导出数据"按钮将对冲历史导出为CSV文件。

## 技术说明

### 对冲算法

本软件使用改进的凯利公式计算对冲机会和最优下注金额分配。当两个平台的赔率差异足够大时，可以通过在两个平台分别下注实现无风险收益。

### 防风险机制

- 自动计算最优下注金额分配
- 防止同平台对冲
- 防止同队伍对冲
- 余额不足保护
- 对冲前的赔率验证

## 免责声明

本软件仅供学习和研究使用，不鼓励任何形式的赌博活动。作者不对使用本软件产生的任何损失或法律问题负责。
